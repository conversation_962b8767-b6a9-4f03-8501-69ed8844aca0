import { Injectable, Logger } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import {
  ApiKey,
  AppInstallation,
  User
} from "@repo/thena-platform-entities";
import { Repository } from "typeorm";

export interface PrivilegedAppValidationResult {
  isValid: boolean;
  user?: User;
  error?: string;
  metadata?: {
    originalBotUserId: string;
    impersonatedUserId: string;
    appId: string;
    organizationId: string;
  };
}

@Injectable()
export class PrivilegedAppService {
  private readonly logger = new Logger(PrivilegedAppService.name);

  constructor(
    @InjectRepository(ApiKey)
    private readonly apiKeyRepository: Repository<ApiKey>,
    @InjectRepository(AppInstallation)
    private readonly appInstallationRepository: Repository<AppInstallation>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * Checks if an API key belongs to a privileged app
   * @param apiKey The full API key (keyId.keySecret)
   * @returns Promise<boolean> indicating if the app is privileged
   */
  async isPrivilegedApp(apiKey: string): Promise<boolean> {
    try {
      const [keyId] = apiKey.split('.');
      if (!keyId) {
        return false;
      }

      // First, find the API key and user
      const apiKeyRecord = await this.apiKeyRepository
        .createQueryBuilder('apiKey')
        .leftJoinAndSelect('apiKey.user', 'user')
        .where('apiKey.keyId = :keyId', { keyId })
        .andWhere('apiKey.isActive = :isActive', { isActive: true })
        .getOne();

      if (!apiKeyRecord) {
        return false;
      }

      // Then check if this user's app is privileged
      const appInstallation = await this.appInstallationRepository
        .createQueryBuilder('appInstallation')
        .leftJoinAndSelect('appInstallation.app', 'app')
        .where('appInstallation.botUserId = :botUserId', { botUserId: apiKeyRecord.user.uid })
        .andWhere('appInstallation.status = :status', { status: 'active' })
        .andWhere('app.isThenaPrivileged = :isPrivileged', { isPrivileged: true })
        .getOne();

      return !!appInstallation;
    } catch (error) {
      this.logger.error('Error checking if app is privileged', {
        error: error.message,
      });
      return false;
    }
  }

  /**
   * Validates user impersonation for a privileged app
   * @param apiKey The full API key
   * @param userId The user ID to impersonate
   * @returns Promise<PrivilegedAppValidationResult>
   */
  async getUserInSameOrganization(
    apiKey: string,
    userId: string,
  ): Promise<PrivilegedAppValidationResult> {
    try {
      const [keyId, keySecret] = apiKey.split('.');

      if (!keyId || !keySecret) {
        return {
          isValid: false,
          error: 'Invalid API key format',
        };
      }

      // First, get the API key and bot user information
      const apiKeyRecord = await this.apiKeyRepository
        .createQueryBuilder('apiKey')
        .leftJoinAndSelect('apiKey.user', 'botUser')
        .leftJoinAndSelect('botUser.organization', 'botOrganization')
        .where('apiKey.keyId = :keyId', { keyId })
        .andWhere('apiKey.isActive = :isActive', { isActive: true })
        .getOne();

      if (!apiKeyRecord) {
        return {
          isValid: false,
          error: 'Invalid API key',
        };
      }

      // Check if the API key belongs to a privileged app
      const isPrivileged = await this.isPrivilegedApp(apiKey);
      if (!isPrivileged) {
        return {
          isValid: false,
          error: 'API key does not belong to a privileged app',
        };
      }

      // Find the target user to impersonate
      const targetUser = await this.userRepository
        .createQueryBuilder('user')
        .leftJoinAndSelect('user.organization', 'organization')
        .where('user.uid = :userId', { userId })
        .getOne();

      if (!targetUser) {
        return {
          isValid: false,
          error: 'Invalid user ID or user not found in organization',
        };
      }

      // Verify both users are in the same organization
      if (targetUser.organizationId !== apiKeyRecord.user.organizationId) {
        this.logger.warn('Organization mismatch in privileged app impersonation', {
          targetUserOrgId: targetUser.organizationId,
          botOrgId: apiKeyRecord.user.organizationId,
        });
        return {
          isValid: false,
          error: 'Invalid user ID or user not found in organization',
        };
      }

      const appInstallation = await this.appInstallationRepository
        .createQueryBuilder('appInstallation')
        .leftJoinAndSelect('appInstallation.app', 'app')
        .where('appInstallation.botUserId = :botUserId', {
          botUserId: apiKeyRecord.user.uid
        })
        .andWhere('appInstallation.status = :status', { status: 'active' })
        .andWhere('app.isThenaPrivileged = :isPrivileged', { isPrivileged: true })
        .getOne();

      if (!appInstallation) {
        return {
          isValid: false,
          error: 'App installation is not active',
        };
      }

      return {
        isValid: true,
        user: targetUser,
        metadata: {
          originalBotUserId: apiKeyRecord.user.id,
          impersonatedUserId: targetUser.id,
          appId: appInstallation.app.id,
          organizationId: targetUser.organizationId,
        },
      };
    } catch (error) {
      this.logger.error('Error validating user impersonation', {
        error: error.message,
        userId,
      });

      return {
        isValid: false,
        error: 'Internal server error during validation',
      };
    }
  }
}
