import { Injectable, Logger, UnauthorizedException } from "@nestjs/common";
import {
  AuthenticationGrpcClient,
  BaseAuthStrategy,
  IHttpAuthStrategy,
  User
} from "@repo/nestjs-commons/guards";
import { FastifyRequest } from "fastify";
import { PrivilegedAppService } from "../services/privileged-app.service";

@Injectable()
export class PrivilegedAppAuthStrategy
  extends BaseAuthStrategy
  implements IHttpAuthStrategy
{
  private readonly logger = new Logger(PrivilegedAppAuthStrategy.name);

  constructor(
    private readonly authClient: AuthenticationGrpcClient,
    private readonly privilegedAppService: PrivilegedAppService,
  ) {
    super();
  }

  /**
   * Authenticates the user using the privileged app strategy with user impersonation.
   * This strategy is used when both X-api-key and X-user-ID headers are present.
   * @param request The request object.
   * @returns A promise that resolves to a User object (the impersonated user).
   */
  async authenticate(request: FastifyRequest): Promise<User> {
    const apiKey = request.headers["x-api-key"] as string;
    const userIdToImpersonate = request.headers["x-user-id"] as string;

    if (!apiKey) {
      throw new UnauthorizedException("API key is required");
    }

    if (!userIdToImpersonate) {
      throw new UnauthorizedException("X-user-ID header is required for privileged app authentication");
    }

    // First, validate the API key normally
    const botUser = await this.authClient.validateKey(apiKey);
    if (!botUser) {
      throw new UnauthorizedException("Invalid API key");
    }

    // Then, validate the privileged app impersonation
    const validationResult = await this.privilegedAppService.getUserInSameOrganization(
      apiKey,
      userIdToImpersonate
    );

    if (!validationResult.isValid) {
      this.logger.warn('Privileged app authentication failed', {
        error: validationResult.error,
        botUserId: botUser.sub,
        requestedUserId: userIdToImpersonate,
        orgId: botUser.orgId,
      });
      throw new UnauthorizedException(validationResult.error);
    }

    // Create the impersonated user object with audit metadata
    // Note: We need to return a User object that matches the commons interface
    // but also includes the platform-specific properties in metadata
    const impersonatedUser: User = {
      authId: validationResult.user.authId || '',
      sub: validationResult.user.id,
      uid: validationResult.user.uid,
      email: validationResult.user.email,
      userName: validationResult.user.email.split("@")[0],
      userType: validationResult.user.userType,
      scopes: botUser.scopes, // Keep the bot's scopes
      orgId: validationResult.user.organizationId,
      orgUid: validationResult.user.organization.uid,
      orgTier: validationResult.user.organization.tier,
      metadata: {
        ...validationResult.user.metadata,
        userType: validationResult.user.userType,
        timezone: validationResult.user.timezone || 'UTC',
        // Add impersonation metadata for audit purposes
        impersonation: {
          isImpersonated: true,
          originalBotUserId: validationResult.metadata.originalBotUserId,
          impersonatedUserId: validationResult.metadata.impersonatedUserId,
          appId: validationResult.metadata.appId,
          organizationId: validationResult.metadata.organizationId,
          impersonatedAt: new Date().toISOString(),
        },
      },
      // Add platform-specific properties that will be used by the platform's custom.d.ts
      token: botUser.token,
    };

    return this.standardizeUser(impersonatedUser);
  }

  /**
   * Checks if this strategy should be used for the given request.
   * Returns true if both X-api-key and X-user-ID headers are present.
   */
  static shouldUse(request: FastifyRequest): boolean {
    return !!(request.headers["x-api-key"] && request.headers["x-user-id"]);
  }
}
